import React, { useState, useRef, useEffect } from 'react';
import ConversationPane from './ConversationPane.jsx';
import SummaryPane from './SummaryPane.jsx';
import LoadingSpinner from './LoadingSpinner.jsx';
import ErrorBoundary, { useErrorHandler } from './ErrorBoundary.jsx';

/**
 * App - 主应用容器
 *
 * - 将 ConversationPane 与 SummaryPane 组合为左右双栏（桌面）/单栏（移动）布局
 * - 管理 messages 与 summaries 的共享状态
 * - 实现 Summary 点击跳转到对应 message 的逻辑（通过 ConversationPane 暴露的 ref）
 *
 * 参考文件：
 * - [`src/frontend/components/ConversationPane.jsx`](src/frontend/components/ConversationPane.jsx:1)
 * - [`src/frontend/components/SummaryPane.jsx`](src/frontend/components/SummaryPane.jsx:1)
 */
export default function App() {
  // 状态管理
  const [messages, setMessages] = useState([]);
  const [summaries, setSummaries] = useState([]);
  const [isLoading, setIsLoading] = useState(true);
  const [loadingText, setLoadingText] = useState('正在加载数据...');

  // 当前选用的 topic id（从URL参数获取，默认为1）
  const [currentTopicId] = useState(() => {
    const urlParams = new URLSearchParams(window.location.search);
    const topicParam = urlParams.get('topic');
    return topicParam ? parseInt(topicParam, 10) : 1;
  });

  // 错误处理
  const { error, resetError, captureError } = useErrorHandler();
  
  // 在挂载时从后端拉取 conversation 与 summaries
  useEffect(() => {
    let mounted = true;
    async function loadData() {
      try {
        setIsLoading(true);
        setLoadingText('正在加载对话数据...');

        // 拉取消息（API: GET /api/topics/:topic_id/conversation 或 /messages）
        const convRes = await fetch(`/api/topics/${currentTopicId}/conversation?limit=200`);
        if (convRes.ok) {
          const convJson = await convRes.json();
          // 后端返回结构在不同端点可能差异，尝试兼容常见字段
          const msgs = convJson.results || convJson.items || convJson.messages || convJson.data || convJson || [];
          if (mounted) {
            setMessages(Array.isArray(msgs) ? msgs.map(m => ({
              message_id: m.message_id || m.id,
              sender: m.role || m.sender || m.role,
              content: m.content || m.snippet || m.text || ''
            })) : []);
          }
        }
      } catch (e) {
        console.error('加载 conversation 失败', e);
        if (mounted) {
          captureError(new Error(`加载对话失败: ${e.message}`));
        }
      }

      try {
        setLoadingText('正在加载摘要数据...');

        // 拉取摘要（API: GET /api/topics/:topic_id/summaries 或 /summary）
        const sumsRes = await fetch(`/api/topics/${currentTopicId}/summaries`);
        if (sumsRes.ok) {
          const sumsJson = await sumsRes.json();
          const items = sumsJson.results || sumsJson.items || sumsJson || [];
          if (mounted) {
            setSummaries(Array.isArray(items) ? items.map(s => ({
              summary_id: s.summary_id || s.id || s.message_id,
              title: s.title || s.label || s.label,
              text: s.summary || s.text || s.snippet || '',
              related_message_id: (s.click_metadata && s.click_metadata.message_id) || s.message_id || s.related_message_id || null
            })) : []);
          }
        } else {
          // 有些实现使用 /summary 返回 recent summaries
          const sumsRes2 = await fetch(`/api/topics/${currentTopicId}/summary`);
          if (sumsRes2.ok) {
            const sumsJson2 = await sumsRes2.json();
            const items2 = sumsJson2.results || sumsJson2.items || sumsJson2 || [];
            if (mounted) {
              setSummaries(Array.isArray(items2) ? items2.map(s => ({
                summary_id: s.summary_id || s.id || s.message_id,
                title: s.title || s.label || `摘要 ${s.summary_id || s.id || ''}`,
                text: s.summary || s.text || s.snippet || '',
                related_message_id: (s.click_metadata && s.click_metadata.message_id) || s.message_id || s.related_message_id || null
              })) : []);
            }
          }
        }
      } catch (e) {
        console.error('加载 summaries 失败', e);
        if (mounted) {
          captureError(new Error(`加载摘要失败: ${e.message}`));
        }
      } finally {
        if (mounted) {
          setIsLoading(false);
        }
      }
    }
    loadData();
    return () => { mounted = false; };
  }, [currentTopicId, captureError]);

  // ref 用于接收 ConversationPane 暴露的 scrollToMessage 函数
  // ConversationPane 支持 ref 对象：onScrollToMessage.current = scrollToMessage
  const scrollRef = useRef(null);

  // 简单的 mock AI 回复生成器（可替换为真实 AI 服务）
  // - 返回字符串作为 assistant 的回复
  // - 可以基于用户输入做简单变换以模拟“理解”
  async function generateMockAIResponse(userText) {
    // 模拟一定的思考时间（例如 400-1200ms）
    const delay = 400 + Math.floor(Math.random() * 800);
    await new Promise(res => setTimeout(res, delay));
    // 简单的回声 + 变换作为示例
    if (!userText || typeof userText !== 'string' || userText.trim() === '') {
      return "抱歉，我没有收到有效的消息内容。可以再试一次吗？";
    }
    return `这是模拟的 AI 回复： ${userText.trim().slice(0, 200)} — 如果需要更详细的回答，请告诉我。`;
  }
  
  // 发送消息到后端API并更新本地状态（包含发送后触发 AI 自动回复的逻辑）
  async function handleSend(text) {
    try {
      // 构造请求体
      const messageData = {
        role: 'user',
        content: text
      };
  
      // 发送到后端API
      const response = await fetch(`/api/topics/${currentTopicId}/messages`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(messageData)
      });
  
      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        throw new Error(errorData.error || `HTTP ${response.status}: ${response.statusText}`);
      }
  
      // 获取后端返回的消息对象（用户消息）
      const savedMessage = await response.json();
  
      // 将后端返回的消息添加到本地状态（用户消息）
      const newMessage = {
        message_id: savedMessage.message_id || savedMessage.id,
        sender: savedMessage.role || 'user',
        content: savedMessage.content,
        created_at: savedMessage.created_at
      };
  
      setMessages(prev => [...prev, newMessage]);
  
      // ---- 开始 AI 自动回复逻辑 ----
      // 在发送成功后异步生成并发送 AI 回复，不要阻塞 handleSend 的返回（但这里我们等待以便测试/同步更新）
      (async () => {
        // 在 UI 中先添加一个 loading 占位消息（例如 sender: 'assistant', content: '' 并带有临时 id）
        const loadingId = `ai-loading-${Date.now()}`;
        const loadingMessage = {
          message_id: loadingId,
          sender: 'assistant',
          content: '',
          created_at: new Date().toISOString(),
          _isLoading: true
        };
        // 添加 loading 占位
        setMessages(prev => [...prev, loadingMessage]);
  
        try {
          // 生成 AI 回复（mock）
          const aiResponse = await generateMockAIResponse(text);
  
          // 构造 assistant 消息体并发送到后端
          const assistantBody = {
            role: 'assistant',
            content: aiResponse
          };
  
          // 模拟网络延迟：额外等待以更真实地模拟 AI 响应时间（可被测试用例 mock）
          await new Promise(res => setTimeout(res, 200));
  
          const aiResp = await fetch(`/api/topics/${currentTopicId}/messages`, {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json'
            },
            body: JSON.stringify(assistantBody)
          });
  
          if (!aiResp.ok) {
            // 若后端保存 AI 回复失败，记录日志并将 loading 占位替换为失败提示（但不影响已发送的用户消息）
            const errData = await aiResp.json().catch(() => ({}));
            console.error('AI 回复发送失败', errData);
            setMessages(prev => prev.map(m => m.message_id === loadingId ? {
              ...m,
              _isLoading: false,
              content: '[AI 回复发送失败]'
            } : m));
            return;
          }
  
          const savedAiMessage = await aiResp.json();
          const assistantMessage = {
            message_id: savedAiMessage.message_id || savedAiMessage.id || `ai-${Date.now()}`,
            sender: savedAiMessage.role || 'assistant',
            content: savedAiMessage.content || aiResponse,
            created_at: savedAiMessage.created_at || new Date().toISOString()
          };
  
          // 将 loading 占位替换为最终的 AI 回复（使用 message_id 匹配）
          setMessages(prev => prev.map(m => m.message_id === loadingId ? assistantMessage : m));
        } catch (aiErr) {
          console.error('生成或发送 AI 回复时出错', aiErr);
          // 将 loading 占位替换为失败提示
          setMessages(prev => prev.map(m => m.message_id === loadingId ? {
            ...m,
            _isLoading: false,
            content: '[AI 回复失败]'
          } : m));
        }
      })();
      // ---- 结束 AI 自动回复逻辑 ----
  
    } catch (e) {
      console.error('发送消息失败', e);
      captureError(new Error(`发送消息失败: ${e.message}`));
      // 重新抛出错误，让ConversationPane知道发送失败
      throw e;
    }
  }

  // 当 Summary 被选中时：尝试使用 related_message_id 或从 summary 内容推断目标 message_id
  function handleSummarySelect(summary) {
    let targetMessageId = summary.related_message_id || summary.message_id || summary.target_message_id;

    // 如果 summary 中没有直接字段，尝试从文本里查找已知 message 内容（简单匹配）
    if (!targetMessageId) {
      for (const m of messages) {
        if (summary.text && summary.text.includes(m.content)) {
          targetMessageId = m.message_id || m.id;
          break;
        }
      }
    }

    if (targetMessageId && scrollRef && scrollRef.current) {
      // scrollRef.current 应为 ConversationPane 暴露的 scrollTo(messageId) 函数
      try {
        scrollRef.current(targetMessageId, { highlight: true, behavior: 'smooth' });
      } catch (e) {
        // 兼容性回退：如果 scrollRef.current 为一个对象（ref 风格）
        if (typeof scrollRef.current === 'object' && scrollRef.current.current) {
          scrollRef.current.current(targetMessageId, { highlight: true, behavior: 'smooth' });
        }
      }
    }
  }

  // 简单响应式样式：左右两列在宽屏显示，窄屏垂直堆叠
  const containerStyle = {
    display: 'flex',
    flexDirection: 'row',
    height: '100vh',
    width: '100%',
  };

  const leftStyle = {
    flex: 1,
    minWidth: 0,
    display: 'flex',
    flexDirection: 'column'
  };

  const rightStyle = {
    width: 360,
    borderLeft: '1px solid #eee',
    overflow: 'auto',
    backgroundColor: '#fafafa'
  };

  // media query fallback: if window is narrow, stack columns
  const [isNarrow, setIsNarrow] = useState(false);
  useEffect(() => {
    function onResize() {
      setIsNarrow(window.innerWidth < 800);
    }
    onResize();
    window.addEventListener('resize', onResize);
    return () => window.removeEventListener('resize', onResize);
  }, []);
  // Global keyboard shortcuts (UX enhancements)
  // - Ctrl/Cmd+Enter: when focus is in the message input, trigger send via ConversationPane's onSend (handled locally in ConversationPane)
  // - Esc: delegate to child components (ConversationPane / SummaryPane) which already listen for Escape to clear highlights
  // We add a no-op global listener here to centralize future global shortcuts and to avoid duplicate behaviors.
  useEffect(() => {
    function onGlobalKeyDown(e) {
      // Avoid interfering with typing in inputs; child components handle their own shortcuts.
      const tag = e.target && e.target.tagName ? e.target.tagName.toUpperCase() : '';
      const editableTags = ['INPUT', 'TEXTAREA', 'SELECT'];
      if (editableTags.includes(tag)) {
        // Let the focused input handle its own shortcuts (ConversationPane handles Ctrl/Cmd+Enter and Shift+Enter)
        return;
      }

      // Example: If we want to provide a global help shortcut in future (e.g., ?), handle here.
      // For now, keep this listener lightweight.
      if (e.key === '?') {
        // prevent default to avoid browser find-in-page in some layouts
        e.preventDefault();
        // Could open a keyboard help modal in future
      }
    }

    window.addEventListener('keydown', onGlobalKeyDown, true);
    return () => window.removeEventListener('keydown', onGlobalKeyDown, true);
  }, []);

  // 如果有错误，显示错误界面
  if (error) {
    return (
      <div style={{
        height: '100vh',
        width: '100%',
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center'
      }}>
        <div style={{
          padding: 20,
          margin: 20,
          border: '1px solid #ff6b6b',
          borderRadius: 8,
          backgroundColor: '#fff5f5',
          color: '#c92a2a',
          maxWidth: 500
        }}>
          <h3 style={{ margin: '0 0 12px 0', color: '#c92a2a' }}>
            😵 应用加载失败
          </h3>
          <p style={{ margin: '0 0 16px 0', color: '#666' }}>
            {error.message || '应用遇到了问题，请尝试刷新页面。'}
          </p>
          <div style={{ display: 'flex', gap: 8 }}>
            <button
              onClick={resetError}
              style={{
                padding: '8px 16px',
                backgroundColor: '#3498db',
                color: 'white',
                border: 'none',
                borderRadius: 4,
                cursor: 'pointer',
                fontSize: 14
              }}
            >
              重试
            </button>
            <button
              onClick={() => window.location.reload()}
              style={{
                padding: '8px 16px',
                backgroundColor: '#95a5a6',
                color: 'white',
                border: 'none',
                borderRadius: 4,
                cursor: 'pointer',
                fontSize: 14
              }}
            >
              刷新页面
            </button>
          </div>
        </div>
      </div>
    );
  }

  // 如果正在加载，显示加载界面
  if (isLoading) {
    return (
      <div style={{
        height: '100vh',
        width: '100%',
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
        flexDirection: 'column',
        gap: 16
      }}>
        <LoadingSpinner size={32} />
        <div style={{ color: '#666', fontSize: 16 }}>{loadingText}</div>
      </div>
    );
  }

  return (
    <ErrorBoundary>
      <div style={{ height: '100vh', width: '100%' }}>
        <div style={isNarrow ? { ...containerStyle, flexDirection: 'column' } : containerStyle}>
          <div style={leftStyle}>
            <ErrorBoundary>
              <ConversationPane
                messages={messages}
                onSend={handleSend}
                onScrollToMessage={scrollRef}
                enableLazyLoading={false}
                autoScrollToBottom={true}
              />
            </ErrorBoundary>
          </div>

          <div style={isNarrow ? { ...rightStyle, width: '100%', borderLeft: 'none', borderTop: '1px solid #eee' } : rightStyle}>
            <ErrorBoundary>
              <SummaryPane
                summaries={summaries}
                onSelect={handleSummarySelect}
              />
            </ErrorBoundary>
          </div>
        </div>
      </div>
    </ErrorBoundary>
  );
}
