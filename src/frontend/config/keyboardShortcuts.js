// Default keyboard shortcuts configuration
const DEFAULT_SHORTCUTS = {
  send: { key: 'Enter', ctrl: true }, // Ctrl/Cmd + Enter
  newline: { key: 'Enter', shift: true }, // Shift + Enter
  clearHighlight: { key: 'Escape' },
  navUp: { key: 'ArrowUp' },
  navDown: { key: 'ArrowDown' }
};

// Get shortcuts with override support
// Priority: props > window.__APP_SHORTCUTS__ > DEFAULT_SHORTCUTS
function getShortcuts(overrides = null) {
  const windowOverrides = (typeof window !== 'undefined' && window.__APP_SHORTCUTS__) || {};
  const propsOverrides = overrides || {};

  return {
    ...DEFAULT_SHORTCUTS,
    ...windowOverrides,
    ...propsOverrides
  };
}

// Export the current shortcuts (can be overridden)
export const SHORTCUTS = getShortcuts();

// Export DEFAULT_SHORTCUTS for testing and reference
export { DEFAULT_SHORTCUTS };

// Helper to match a KeyboardEvent against a shortcut definition.
// shortcutDef can be a string key (e.g. 'Escape') or an object as above.
export function matchesShortcut(event, shortcutDef) {
  if (!event || !shortcutDef) return false;

  if (typeof shortcutDef === 'string') {
    return event.key === shortcutDef;
  }

  const keyMatch = event.key === shortcutDef.key;
  if (!keyMatch) return false;

  // if ctrl is specified, require ctrlKey or metaKey (for macOS Cmd)
  if (shortcutDef.ctrl === true) {
    if (!(event.ctrlKey || event.metaKey)) return false;
  }
  if (shortcutDef.ctrl === false) {
    if (event.ctrlKey || event.metaKey) return false;
  }

  if (shortcutDef.shift === true && !event.shiftKey) return false;
  if (shortcutDef.shift === false && event.shiftKey) return false;

  // other modifiers could be added if needed
  return true;
}

// Helper function to get shortcuts with runtime overrides
export function getShortcutsWithOverrides(overrides = null) {
  return getShortcuts(overrides);
}

// Helper function to update global shortcuts at runtime
export function updateGlobalShortcuts(newShortcuts) {
  if (typeof window !== 'undefined') {
    window.__APP_SHORTCUTS__ = { ...window.__APP_SHORTCUTS__, ...newShortcuts };
  }
}

export default {
  SHORTCUTS,
  DEFAULT_SHORTCUTS,
  getShortcuts,
  getShortcutsWithOverrides,
  updateGlobalShortcuts,
  matchesShortcut
};